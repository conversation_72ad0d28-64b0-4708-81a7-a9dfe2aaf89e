@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .animate-bounce {
    animation: bounce 2s infinite;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) skewX(-12deg);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(200%) skewX(-12deg);
    opacity: 0;
  }
}

@keyframes smoothGrow {
  0% {
    transform: scaleX(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  100% {
    transform: scaleX(1);
    opacity: 1;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(10deg);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animate-float-slow {
  animation: float 6s ease-in-out infinite;
}

.animate-shimmer {
  animation: shimmer 2.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  animation-delay: 1s;
}

.animate-smooth-grow {
  animation: smoothGrow 1.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animate-card-pulse {
  animation: cardPulse 2s infinite;
}

.animate-card-float {
  animation: cardFloat 3s ease-in-out infinite;
}

/* Line clamp utility */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Preview tile animations */
@keyframes previewSlideIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes previewFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.preview-tile {
  animation: previewSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 3D Flip Card Styles */
.perspective-1000 {
  perspective: 1000px;
}

.transform-style-preserve-3d {
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
}

.rotate-y-180 {
  transform: rotateY(180deg);
}

/* Flip animation keyframes */
@keyframes flipIn {
  from {
    transform: rotateY(-180deg);
  }
  to {
    transform: rotateY(0deg);
  }
}

@keyframes flipOut {
  from {
    transform: rotateY(0deg);
  }
  to {
    transform: rotateY(180deg);
  }
}

/* Card hover effects */
.flip-card:hover .flip-card-inner {
  transform: rotateY(180deg);
}

.flip-card-inner {
  transition: transform 0.7s cubic-bezier(0.4, 0, 0.2, 1);
  transform-style: preserve-3d;
}

/* Additional flip animations */
@keyframes cardFlipHover {
  0% {
    transform: rotateY(0deg) scale(1);
  }
  50% {
    transform: rotateY(90deg) scale(1.05);
  }
  100% {
    transform: rotateY(180deg) scale(1);
  }
}

.flip-card-enhanced:hover .flip-card-inner {
  animation: cardFlipHover 0.7s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Glow effect for flip cards */
.flip-card-glow {
  transition: all 0.3s ease;
}

.flip-card-glow:hover {
  box-shadow: 0 0 30px rgba(6, 182, 212, 0.3), 0 0 60px rgba(6, 182, 212, 0.1);
}

.animation-delay-1000 {
  animation-delay: 1s;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-3000 {
  animation-delay: 3s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* 3D Cube */
.cube {
  width: 100%;
  height: 100%;
  position: relative;
  transform-style: preserve-3d;
  animation: rotate 20s infinite linear;
}

.cube-face {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0.8;
}

.front  { transform: rotateY(0deg) translateZ(32px); }
.back   { transform: rotateY(180deg) translateZ(32px); }
.right  { transform: rotateY(90deg) translateZ(32px); }
.left   { transform: rotateY(-90deg) translateZ(32px); }
.top    { transform: rotateX(90deg) translateZ(32px); }
.bottom { transform: rotateX(-90deg) translateZ(32px); }

/* 3D Pyramid */
.pyramid {
  width: 0;
  height: 0;
  position: relative;
  transform-style: preserve-3d;
  animation: rotate 15s infinite linear;
}

.pyramid-face {
  position: absolute;
  width: 0;
  height: 0;
  border-left: 32px solid transparent;
  border-right: 32px solid transparent;
  border-bottom: 64px solid;
  opacity: 0.8;
}

.pyramid .front { transform: rotateX(-30deg); }
.pyramid .right { transform: rotateY(120deg) rotateX(-30deg); }
.pyramid .left { transform: rotateY(-120deg) rotateX(-30deg); }
.pyramid .back { transform: rotateY(180deg) rotateX(-30deg); }

@keyframes rotate {
  from {
    transform: rotateX(0) rotateY(0) rotateZ(0);
  }
  to {
    transform: rotateX(360deg) rotateY(360deg) rotateZ(360deg);
  }
}

@keyframes cardPulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(6, 182, 212, 0.4);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(6, 182, 212, 0);
  }
}

@keyframes cardFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-5px) rotate(1deg);
  }
}